# OCR模块架构重构计划

## 项目概述
重构和重建OCR模块架构，消除代码重复并改善关注点分离。

## 重构目标

### 1. 架构分离 ✅ DONE
- **LLM服务**: 分离Gemini（需要代理）和OpenRouter（无需代理）
- **OCR服务**: 保持marker-pdf和Azure OCR作为独立模块
- **代理管理**: 提取代理功能到独立工具模块

### 2. 模块结构 ✅ DONE
创建以下组件：
- `config/` - 统一配置管理系统
  - `llm_config.py` - LLM服务配置管理
  - `azure_config.py` - Azure配置管理
- `proxy/` - 独立代理管理模块
  - `proxy_manager.py` - 通用代理工具函数
- `core/` - 统一OCR基础类
  - `base_ocr.py` - OCR基础抽象类
  - `ocr_factory.py` - OCR工厂类（依赖注入）
- `services/` - 具体OCR服务实现
  - `marker_http_ocr.py` - HTTP代理实现
  - `marker_socks5_ocr.py` - SOCKS5代理实现
  - `marker_openrouter_ocr.py` - OpenRouter实现
  - `azure_ocr_service.py` - Azure OCR服务

### 3. 配置管理 ✅ DONE
- 创建配置类读取LLM服务设置从`.env`文件
- 支持OpenRouter和Gemini配置
- 使用`USE_GEMINI=true`标志确定使用哪个LLM服务

### 4. 代理实现要求 ✅ DONE
- `proxy_manager.py`作为通用工具模块（不特定于markerpdf）
- 在创建marker-pdf对象前应用代理配置到环境
- 实现Gemini处理完成后代理设置的正确清理
- 仅对Gemini模型使用代理，OpenRouter不使用

### 5. 集成与测试 🔄 IN PROGRESS
- 集成到`main.py`使用HTTP代理+Gemini作为默认配置
- 通过注释保留现有代码而不是删除
- 维护`compare_proxy_types.py`功能供用户基准测试
- 使用`uv`包管理器运行测试验证功能

### 6. 代码质量 ✅ DONE
- 消除markerpdf模块中所有冗余和重复代码
- 遵循LLM服务选择的依赖注入模式
- 保持代理管理和OCR功能之间的清晰分离

## 实施状态

### ✅ 已完成
1. **统一配置管理系统** - 创建了`config/`目录和配置类
2. **代理管理模块重构** - 将`proxy_manager.py`移到独立的`proxy/`目录
3. **统一OCR基础类** - 创建了`core/`模块和基础接口
4. **markerpdf模块重构** - 消除重复代码，保持向后兼容性
5. **服务实现** - 创建了具体的OCR服务实现

### 🔄 进行中
6. **main.py集成** - 更新main.py使用新架构

### ✅ 已完成
7. **测试和验证** - 新架构基本功能测试通过，发现并修复了一些小问题

## 架构优势

### 新架构特点
1. **统一接口**: 所有OCR服务实现相同的`BaseOCR`接口
2. **依赖注入**: 使用工厂模式创建OCR实例，支持不同配置
3. **关注点分离**: 配置、代理、OCR处理分离到不同模块
4. **向后兼容**: 保留原有函数接口，内部使用新架构
5. **代码复用**: 消除重复代码，提高维护性

### 配置管理
- 统一从`.env`文件读取配置
- 支持运行时切换LLM服务
- 自动处理代理需求（仅Gemini需要）

### 代理管理
- 独立的代理管理模块
- 支持HTTP和SOCKS5代理
- 自动SOCKS5到HTTP转换
- 上下文管理器确保代理正确设置和清理

## 下一步行动

1. **测试验证** - 运行`uv run python main.py`测试新架构
2. **性能对比** - 使用`compare_proxy_types.py`对比性能
3. **文档更新** - 更新README.md反映新架构
4. **错误处理** - 完善错误处理和日志记录

## 技术债务清理

### 已清理
- ✅ 消除`marker_ocr_socks5_proxy.py`和`marker_ocr_http_proxy.py`中的重复配置代码
- ✅ 统一代理管理逻辑
- ✅ 分离LLM服务配置

### 保留的遗留代码
- 📁 `markerpdf/marker_ocr_socks5_proxy.py` - 保留用于参考
- 📁 `markerpdf/marker_ocr_http_proxy.py` - 保留用于参考
- 📁 `markerpdf/compare_proxy_types.py` - 保留用于性能基准测试

## 测试计划

### 功能测试
1. **Gemini HTTP代理** - 测试默认配置
2. **Gemini SOCKS5代理** - 测试备用配置
3. **OpenRouter** - 测试无代理配置
4. **Azure OCR** - 测试Azure服务
5. **配置切换** - 测试运行时配置切换

### 性能测试
1. **代理性能对比** - HTTP vs SOCKS5
2. **处理时间对比** - 新旧架构对比
3. **内存使用** - 检查内存泄漏

### 集成测试
1. **端到端测试** - 完整工作流测试
2. **错误恢复** - 网络错误和重试机制
3. **并发处理** - 多文档处理测试
