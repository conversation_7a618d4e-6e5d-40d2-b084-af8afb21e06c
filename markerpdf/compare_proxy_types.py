#!/usr/bin/env python3
"""
代理性能比较工具 (新架构版本)

比较SOCKS5和HTTP代理在Gemini API使用中的性能差异
测试两种代理类型的兼容性和行为差异
确保在代理设置完成后才开始计时，以获得准确的性能比较

新架构特性：
- 使用统一的OCRFactory创建OCR实例
- 集成新的配置管理系统
- 利用标准化的OCRResult格式
- 遵循依赖注入模式
"""

import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 新架构导入
from core import OCRFactory
from core.base_ocr import OCRResult
from config import LLMConfigManager
from proxy import ProxyManager


def test_socks5_proxy_with_timing(pdf_path: str = "sample.pdf", max_retries: int = 3) -> Dict[str, Any]:
    """
    使用新架构测试SOCKS5代理性能
    
    Args:
        pdf_path: PDF文件路径
        max_retries: 最大重试次数
        
    Returns:
        包含测试结果的字典
    """
    print(f"\n{'='*50}")
    print("🔍 测试SOCKS5代理性能 (新架构)")
    print(f"{'='*50}")
    
    # 使用新架构创建OCR实例
    factory = OCRFactory()
    config_manager = LLMConfigManager()
    
    # 验证配置
    if not config_manager.validate_config():
        return {
            'proxy_type': 'SOCKS5',
            'success': False,
            'error': 'Gemini配置无效',
            'setup_time': 0,
            'processing_time': 0,
            'total_time': 0,
            'attempts': 0
        }
    
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"\n第 {attempt + 1} 次尝试...")
            
            # 记录总开始时间
            total_start_time = time.time()
            
            # Step 1: 设置SOCKS5代理
            print("🔧 设置SOCKS5代理...")
            setup_start_time = time.time()
            
            # 创建SOCKS5代理的OCR实例
            ocr_instance = factory.create_marker_ocr(proxy_type="socks5")
            
            setup_time = time.time() - setup_start_time
            print(f"✅ SOCKS5代理设置完成 ({setup_time:.2f}秒)")
            
            # Step 2: 执行OCR处理
            print("🔄 开始OCR处理...")
            result = ocr_instance.process_document(pdf_path)
            
            total_time = time.time() - total_start_time
            
            if result.success:
                print(f"✅ SOCKS5代理测试成功！")
                print(f"⏱️ 设置时间: {setup_time:.2f}秒")
                print(f"⏱️ 处理时间: {result.processing_time:.2f}秒")
                print(f"⏱️ 总时间: {total_time:.2f}秒")
                print(f"📄 文本长度: {result.text_length}字符")
                
                return {
                    'proxy_type': 'SOCKS5',
                    'success': True,
                    'error': None,
                    'setup_time': setup_time,
                    'processing_time': result.processing_time,
                    'total_time': total_time,
                    'text_length': result.text_length,
                    'output_folder': result.output_folder,
                    'attempts': attempt + 1,
                    'ocr_result': result
                }
            else:
                error_message = result.error or "OCR处理失败"
                print(f"❌ 第 {attempt + 1} 次尝试失败: {error_message}")
                
        except Exception as e:
            error_message = str(e)
            print(f"❌ 第 {attempt + 1} 次尝试异常: {error_message}")
        
        # 等待后重试
        if attempt < max_retries - 1:
            time.sleep(2)
    
    # 所有尝试都失败
    return {
        'proxy_type': 'SOCKS5',
        'success': False,
        'error': error_message,
        'setup_time': 0,
        'processing_time': 0,
        'total_time': 0,
        'attempts': max_retries
    }


def test_http_proxy_with_timing(pdf_path: str = "sample.pdf", max_retries: int = 3) -> Dict[str, Any]:
    """
    使用新架构测试HTTP代理性能 (SOCKS5转换)
    
    Args:
        pdf_path: PDF文件路径
        max_retries: 最大重试次数
        
    Returns:
        包含测试结果的字典
    """
    print(f"\n{'='*50}")
    print("🔍 测试HTTP代理性能 (SOCKS5->HTTP转换, 新架构)")
    print(f"{'='*50}")
    
    # 使用新架构创建OCR实例
    factory = OCRFactory()
    config_manager = LLMConfigManager()
    
    # 验证配置
    if not config_manager.validate_config():
        return {
            'proxy_type': 'HTTP',
            'success': False,
            'error': 'Gemini配置无效',
            'setup_time': 0,
            'processing_time': 0,
            'total_time': 0,
            'attempts': 0
        }
    
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"\n第 {attempt + 1} 次尝试...")
            
            # 记录总开始时间
            total_start_time = time.time()
            
            # Step 1: 设置HTTP代理 (包含SOCKS5转换)
            print("🔧 设置HTTP代理 (SOCKS5->HTTP转换)...")
            setup_start_time = time.time()
            
            # 创建HTTP代理的OCR实例
            ocr_instance = factory.create_marker_ocr(proxy_type="http")
            
            setup_time = time.time() - setup_start_time
            print(f"✅ HTTP代理设置完成 ({setup_time:.2f}秒)")
            
            # Step 2: 执行OCR处理
            print("🔄 开始OCR处理...")
            result = ocr_instance.process_document(pdf_path)
            
            total_time = time.time() - total_start_time
            
            if result.success:
                print(f"✅ HTTP代理测试成功！")
                print(f"⏱️ 设置时间: {setup_time:.2f}秒 (包含转换)")
                print(f"⏱️ 处理时间: {result.processing_time:.2f}秒")
                print(f"⏱️ 总时间: {total_time:.2f}秒")
                print(f"📄 文本长度: {result.text_length}字符")
                
                return {
                    'proxy_type': 'HTTP',
                    'success': True,
                    'error': None,
                    'setup_time': setup_time,
                    'processing_time': result.processing_time,
                    'total_time': total_time,
                    'text_length': result.text_length,
                    'output_folder': result.output_folder,
                    'attempts': attempt + 1,
                    'ocr_result': result
                }
            else:
                error_message = result.error or "OCR处理失败"
                print(f"❌ 第 {attempt + 1} 次尝试失败: {error_message}")
                
        except Exception as e:
            error_message = str(e)
            print(f"❌ 第 {attempt + 1} 次尝试异常: {error_message}")
        
        # 等待后重试
        if attempt < max_retries - 1:
            time.sleep(2)
    
    # 所有尝试都失败
    return {
        'proxy_type': 'HTTP',
        'success': False,
        'error': error_message,
        'setup_time': 0,
        'processing_time': 0,
        'total_time': 0,
        'attempts': max_retries
    }


def save_comparison_results(socks5_result: Dict[str, Any], http_result: Dict[str, Any]) -> str:
    """
    保存代理比较结果到Markdown文件
    
    Args:
        socks5_result: SOCKS5代理测试结果
        http_result: HTTP代理测试结果
        
    Returns:
        报告文件路径
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"proxy_comparison_results/proxy_comparison_{timestamp}.md"
    
    # 确保目录存在
    Path(report_file).parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# 代理性能比较报告 (新架构)\n\n")
        f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**架构版本**: 2.0 (重构版本)\n")
        f.write(f"**测试目的**: 比较SOCKS5和HTTP代理在Gemini API使用中的性能\n\n")
        
        # 写入总结表格
        f.write("## 性能总结\n\n")
        f.write("| 代理类型 | 状态 | 设置时间(秒) | 处理时间(秒) | 总时间(秒) | 文本长度 | 尝试次数 |\n")
        f.write("|----------|------|-------------|-------------|-----------|----------|----------|\n")
        
        # SOCKS5结果
        socks5_status = "✅ 成功" if socks5_result['success'] else "❌ 失败"
        f.write(f"| SOCKS5 | {socks5_status} | {socks5_result.get('setup_time', 0):.2f} | {socks5_result.get('processing_time', 0):.2f} | {socks5_result.get('total_time', 0):.2f} | {socks5_result.get('text_length', 0)} | {socks5_result.get('attempts', 0)} |\n")
        
        # HTTP结果
        http_status = "✅ 成功" if http_result['success'] else "❌ 失败"
        f.write(f"| HTTP | {http_status} | {http_result.get('setup_time', 0):.2f} | {http_result.get('processing_time', 0):.2f} | {http_result.get('total_time', 0):.2f} | {http_result.get('text_length', 0)} | {http_result.get('attempts', 0)} |\n")
        
        f.write("\n")
        
        # 性能分析
        if socks5_result['success'] and http_result['success']:
            f.write("## 性能分析\n\n")
            
            setup_diff = abs(socks5_result['setup_time'] - http_result['setup_time'])
            processing_diff = abs(socks5_result['processing_time'] - http_result['processing_time'])
            total_diff = abs(socks5_result['total_time'] - http_result['total_time'])
            
            faster_setup = "SOCKS5" if socks5_result['setup_time'] < http_result['setup_time'] else "HTTP"
            faster_processing = "SOCKS5" if socks5_result['processing_time'] < http_result['processing_time'] else "HTTP"
            faster_total = "SOCKS5" if socks5_result['total_time'] < http_result['total_time'] else "HTTP"
            
            f.write(f"- **设置速度**: {faster_setup} 快 {setup_diff:.2f}秒\n")
            f.write(f"- **处理速度**: {faster_processing} 快 {processing_diff:.2f}秒\n")
            f.write(f"- **总体速度**: {faster_total} 快 {total_diff:.2f}秒\n\n")
            
            # 推荐
            if faster_total == "HTTP":
                f.write("### 🏆 推荐配置: HTTP代理\n")
                f.write("HTTP代理 (SOCKS5转换) 在总体性能上表现更好。\n\n")
            else:
                f.write("### 🏆 推荐配置: SOCKS5代理\n")
                f.write("SOCKS5代理在总体性能上表现更好。\n\n")
        
        # 详细结果
        f.write("## 详细测试结果\n\n")
        
        # SOCKS5详细结果
        f.write("### SOCKS5代理测试\n\n")
        if socks5_result['success']:
            f.write(f"- **状态**: ✅ 成功\n")
            f.write(f"- **设置时间**: {socks5_result['setup_time']:.2f}秒\n")
            f.write(f"- **处理时间**: {socks5_result['processing_time']:.2f}秒\n")
            f.write(f"- **总时间**: {socks5_result['total_time']:.2f}秒\n")
            f.write(f"- **文本长度**: {socks5_result['text_length']}字符\n")
            f.write(f"- **输出目录**: {socks5_result.get('output_folder', 'N/A')}\n")
        else:
            f.write(f"- **状态**: ❌ 失败\n")
            f.write(f"- **错误**: {socks5_result.get('error', 'Unknown error')}\n")
        f.write(f"- **尝试次数**: {socks5_result['attempts']}\n\n")
        
        # HTTP详细结果
        f.write("### HTTP代理测试 (SOCKS5转换)\n\n")
        if http_result['success']:
            f.write(f"- **状态**: ✅ 成功\n")
            f.write(f"- **设置时间**: {http_result['setup_time']:.2f}秒 (包含SOCKS5->HTTP转换)\n")
            f.write(f"- **处理时间**: {http_result['processing_time']:.2f}秒\n")
            f.write(f"- **总时间**: {http_result['total_time']:.2f}秒\n")
            f.write(f"- **文本长度**: {http_result['text_length']}字符\n")
            f.write(f"- **输出目录**: {http_result.get('output_folder', 'N/A')}\n")
        else:
            f.write(f"- **状态**: ❌ 失败\n")
            f.write(f"- **错误**: {http_result.get('error', 'Unknown error')}\n")
        f.write(f"- **尝试次数**: {http_result['attempts']}\n\n")
        
        # 技术说明
        f.write("## 技术说明\n\n")
        f.write("### 新架构特性\n")
        f.write("- 使用统一的OCRFactory创建OCR实例\n")
        f.write("- 集成新的配置管理系统\n")
        f.write("- 利用标准化的OCRResult格式\n")
        f.write("- 遵循依赖注入模式\n\n")
        
        f.write("### 代理类型说明\n")
        f.write("- **SOCKS5代理**: 直接使用SOCKS5协议连接\n")
        f.write("- **HTTP代理**: 使用pproxy将SOCKS5转换为HTTP代理\n\n")
        
        f.write("### 性能测量\n")
        f.write("- **设置时间**: 代理配置和初始化时间\n")
        f.write("- **处理时间**: 实际OCR处理时间\n")
        f.write("- **总时间**: 从开始到完成的总时间\n\n")
    
    print(f"✅ 比较报告已保存: {report_file}")
    return report_file


def main(pdf_path: str = "sample.pdf") -> bool:
    """
    主函数：执行代理性能比较测试

    Args:
        pdf_path: 要测试的PDF文件路径

    Returns:
        测试是否成功完成
    """
    print("=" * 70)
    print("🚀 代理性能比较测试 (新架构 v2.0)")
    print("📋 比较SOCKS5和HTTP代理在Gemini API使用中的性能")
    print("🔧 使用统一OCR接口和依赖注入模式")
    print("=" * 70)

    # 验证配置
    config_manager = LLMConfigManager()
    if not config_manager.validate_config():
        print("❌ Gemini配置无效，请检查.env文件")
        return False

    if not config_manager.should_use_gemini():
        print("⚠️ 当前配置未启用Gemini，请设置USE_GEMINI=true")
        return False

    print("✅ 配置验证通过")

    # 检查PDF文件
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return False

    print(f"📄 测试文件: {pdf_path}")

    try:
        # 创建代理管理器用于最终清理
        proxy_manager = ProxyManager()

        # 测试SOCKS5代理
        print(f"\n{'='*50}")
        print("🔍 第一阶段: SOCKS5代理性能测试")
        print(f"{'='*50}")
        socks5_result = test_socks5_proxy_with_timing(pdf_path)

        if socks5_result['success']:
            print(f"✅ SOCKS5测试完成:")
            print(f"   设置时间: {socks5_result['setup_time']:.2f}秒")
            print(f"   处理时间: {socks5_result['processing_time']:.2f}秒")
            print(f"   总时间: {socks5_result['total_time']:.2f}秒")
        else:
            print(f"❌ SOCKS5测试失败: {socks5_result.get('error', 'Unknown error')}")

        # 等待一段时间再进行下一个测试
        print("\n⏳ 等待5秒后开始HTTP代理测试...")
        time.sleep(5)

        # 测试HTTP代理
        print(f"\n{'='*50}")
        print("🔍 第二阶段: HTTP代理性能测试")
        print(f"{'='*50}")
        http_result = test_http_proxy_with_timing(pdf_path)

        if http_result['success']:
            print(f"✅ HTTP测试完成:")
            print(f"   设置时间: {http_result['setup_time']:.2f}秒 (包含转换)")
            print(f"   处理时间: {http_result['processing_time']:.2f}秒")
            print(f"   总时间: {http_result['total_time']:.2f}秒")
        else:
            print(f"❌ HTTP测试失败: {http_result.get('error', 'Unknown error')}")

        # 保存比较结果
        comparison_file = save_comparison_results(socks5_result, http_result)

        # 显示总结
        print("\n" + "=" * 70)
        print("📊 代理性能比较总结:")
        print(f"  SOCKS5: {'✅ 成功' if socks5_result['success'] else '❌ 失败'}")
        if socks5_result['success']:
            print(f"    总时间: {socks5_result['total_time']:.2f}秒")

        print(f"  HTTP:   {'✅ 成功' if http_result['success'] else '❌ 失败'}")
        if http_result['success']:
            print(f"    总时间: {http_result['total_time']:.2f}秒")

        # 性能比较和推荐
        if socks5_result['success'] and http_result['success']:
            total_diff = abs(socks5_result['total_time'] - http_result['total_time'])
            faster = "SOCKS5" if socks5_result['total_time'] < http_result['total_time'] else "HTTP"
            print(f"  🏆 推荐: {faster} (快 {total_diff:.2f}秒)")

            # 详细分析
            print(f"\n📈 详细性能分析:")
            print(f"  设置时间: SOCKS5 {socks5_result['setup_time']:.2f}s vs HTTP {http_result['setup_time']:.2f}s")
            print(f"  处理时间: SOCKS5 {socks5_result['processing_time']:.2f}s vs HTTP {http_result['processing_time']:.2f}s")

        print(f"\n📋 详细报告: {comparison_file}")
        print("=" * 70)

        return True

    except Exception as e:
        print(f"❌ 比较测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        # 清理代理设置
        try:
            proxy_manager = ProxyManager()
            proxy_manager.clear_proxy()
            print("\n🧹 代理设置已清理")
        except:
            pass


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="代理性能比较测试 (新架构)")
    parser.add_argument("--pdf", default="sample.pdf", help="要测试的PDF文件路径")
    args = parser.parse_args()

    success = main(args.pdf)
    if success:
        print("\n🎉 代理性能比较测试完成！")
    else:
        print("\n❌ 代理性能比较测试失败！")

    sys.exit(0 if success else 1)
