# ===== 旧版本代码 - 已重构 =====
# 以下代码已被重构到新的架构中，保留用于参考
"""
# 旧版本 markerpdf package for marker-pdf OCR processing with proxy support
#
# This package contains modules for testing and comparing different proxy types
# (SOCKS5 vs HTTP) with marker-pdf OCR functionality.
#
# 注意：此模块已被重构，新的实现请使用：
# - core.OCRFactory 用于创建OCR实例
# - services.* 用于具体的OCR服务实现
# - proxy.ProxyManager 用于代理管理
# - config.LLMConfigManager 用于配置管理
"""

# 保留向后兼容性的导入
from .legacy_functions import (
    create_gemini_config,
    create_openrouter_config,
    test_model,
    save_results_to_markdown
)

from .compare_proxy_types import (
    test_socks5_proxy_with_timing,
    test_http_proxy_with_timing,
    save_comparison_results,
    main as compare_main
)

# 新架构的推荐导入
from core import OCRFactory
from proxy import ProxyManager

__version__ = "2.0.0"  # 版本升级以反映重构
__author__ = "Brian"

__all__ = [
    # 向后兼容的函数（已重构）
    "create_gemini_config",
    "create_openrouter_config",
    "test_model",
    "save_results_to_markdown",

    # 代理比较功能（保留）
    "test_socks5_proxy_with_timing",
    "test_http_proxy_with_timing",
    "save_comparison_results",
    "compare_main",

    # 新架构推荐使用
    "OCRFactory",
    "ProxyManager"
]