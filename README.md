# OCR Comparison Project

This project compares different OCR models and services for PDF document processing, including marker-pdf with various LLM backends and Azure Document Intelligence.

## Features

- Compare multiple OCR models:
  - Gemini 2.5 Flash (via marker-pdf)
  - OpenRouter Deepseek (via marker-pdf)
  - Azure Document Intelligence
- Enhanced proxy management for API access
- Detailed result comparison and analysis
- Markdown output format with image extraction
- Configurable batch processing and language support
- Async processing for Azure Document Intelligence

## Setup

1. Install dependencies:
```bash
uv sync
```

2. Configure environment variables in `.env`:
```bash
# Copy and modify the .env file with your API keys
```

3. Run the comparison:
```bash
uv run python main.py
```

## Configuration

The project uses environment variables for configuration. See `.env` file for available options:

### Marker-PDF Configuration
- `GEMINI_API_KEY`: Your Google Gemini API key
- `OPENROUTER_API_KEY`: Your OpenRouter API key
- `NORDVPN_PROXY`: SOCKS5 proxy configuration (if needed)
- Various marker-pdf configuration options

### Azure Document Intelligence Configuration
- `AZURE_DOCINTEL_ENDPOINT`: Your Azure Document Intelligence endpoint
- `AZURE_DOCINTEL_KEY`: Your Azure Document Intelligence key
- `AZURE_MODEL_ID`: Model ID to use (default: prebuilt-layout)
- `AZURE_LOCALE`: Language locale (default: en-US)

## Output

Results are saved in the `ocr_comparison_results/` directory with:
- Individual model folders containing OCR results and extracted images
- Comparison summary reports in Markdown format
- Detailed configuration and performance metrics

## Models Supported

1. **Gemini 2.5 Flash**: Google's latest multimodal model via marker-pdf
2. **OpenRouter Deepseek R1**: Advanced reasoning model via OpenRouter and marker-pdf
3. **Azure Document Intelligence**: Microsoft's document analysis service with advanced OCR capabilities

## Project Structure (New Architecture v2.0)

### Core Modules
- `main.py`: Main comparison script using new unified architecture
- `config/`: Unified configuration management system
  - `llm_config.py`: LLM service configuration (Gemini/OpenRouter)
  - `azure_config.py`: Azure Document Intelligence configuration
- `core/`: Unified OCR interfaces and factory pattern
  - `base_ocr.py`: Abstract OCR base class and result format
  - `ocr_factory.py`: OCR factory with dependency injection
- `services/`: Concrete OCR service implementations
  - `marker_http_ocr.py`: Marker OCR with HTTP proxy
  - `marker_socks5_ocr.py`: Marker OCR with SOCKS5 proxy
  - `marker_openrouter_ocr.py`: Marker OCR with OpenRouter
  - `azure_ocr_service.py`: Azure Document Intelligence service
- `proxy/`: Independent proxy management utilities
  - `proxy_manager.py`: Universal proxy management (HTTP/SOCKS5)

### Performance Benchmarking
- `markerpdf/compare_proxy_types.py`: **Updated proxy performance comparison tool**
  - Uses new architecture with OCRFactory and unified configuration
  - Compares SOCKS5 vs HTTP proxy performance for Gemini API
  - Generates detailed performance reports with recommendations
  - Run with: `python markerpdf/compare_proxy_types.py`

### Legacy Modules (Preserved for Reference)
- `markerpdf/`: Legacy marker-pdf integration (with backward compatibility)
- `azure_ocr.py`: Legacy Azure integration (still functional)

## Azure Document Intelligence Features

The Azure integration includes:
- High-resolution OCR capabilities
- Math formula recognition (MATH_SOLVER feature)
- Multi-language support with locale-specific optimization
- Async processing for better performance
- Structured markdown output
- Error handling and retry mechanisms

## Proxy Support

The project includes enhanced proxy management for accessing APIs that may be geo-restricted, particularly useful for Gemini API access.

## Usage Examples

### New Architecture (v2.0) - Recommended

#### Basic OCR Comparison
```bash
# Run complete OCR comparison with new architecture
python main.py
```

#### Proxy Performance Benchmarking
```bash
# Compare SOCKS5 vs HTTP proxy performance
python markerpdf/compare_proxy_types.py

# Test with specific PDF file
python markerpdf/compare_proxy_types.py --pdf your_document.pdf
```

#### Using the New Architecture Programmatically
```python
from core import OCRFactory
from config import LLMConfigManager

# Create OCR factory
factory = OCRFactory()

# Get default OCR instance (HTTP proxy + Gemini)
ocr = factory.get_default_ocr()

# Process document
result = ocr.process_document("sample.pdf")

# Or create specific proxy type
socks5_ocr = factory.create_marker_ocr(proxy_type="socks5")
http_ocr = factory.create_marker_ocr(proxy_type="http")

# Configuration management
config_manager = LLMConfigManager()
if config_manager.should_use_gemini():
    gemini_config = config_manager.create_gemini_config()
```

### Legacy Usage (Still Supported)

### Basic OCR Comparison
```bash
uv run python main.py
```

### Test Azure Document Intelligence Only
```bash
uv run python azure_ocr.py
```

### Batch Processing with Azure
```python
import asyncio
from azure_ocr import batch_analyze_documents

documents = [
    ("document1.pdf", "en-US"),
    ("document2.pdf", "zh-Hant"),
]

results = asyncio.run(batch_analyze_documents(documents))
```