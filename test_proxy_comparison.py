#!/usr/bin/env python3
"""
测试新架构的代理比较工具
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_proxy_comparison_imports():
    """测试代理比较工具的导入"""
    print("🔍 测试代理比较工具导入...")
    
    try:
        from markerpdf.compare_proxy_types import (
            test_socks5_proxy_with_timing,
            test_http_proxy_with_timing,
            save_comparison_results,
            main
        )
        print("✅ 代理比较工具导入成功")
        
        # 测试函数签名
        print("✅ 可用函数:")
        print("  - test_socks5_proxy_with_timing")
        print("  - test_http_proxy_with_timing") 
        print("  - save_comparison_results")
        print("  - main")
        
        return True
    except Exception as e:
        print(f"❌ 代理比较工具导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_validation():
    """测试配置验证"""
    print("\n🔍 测试配置验证...")
    
    try:
        from config import LLMConfigManager
        
        config_manager = LLMConfigManager()
        is_valid = config_manager.validate_config()
        use_gemini = config_manager.should_use_gemini()
        
        print(f"✅ 配置验证: {is_valid}")
        print(f"✅ 使用Gemini: {use_gemini}")
        
        if is_valid and use_gemini:
            print("✅ 配置适合进行代理比较测试")
            return True
        else:
            print("⚠️ 配置不适合进行代理比较测试")
            return False
            
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def test_ocr_factory():
    """测试OCR工厂创建不同代理类型的实例"""
    print("\n🔍 测试OCR工厂代理类型...")
    
    try:
        from core import OCRFactory
        
        factory = OCRFactory()
        print("✅ OCR工厂创建成功")
        
        # 测试创建不同代理类型的OCR实例
        try:
            socks5_ocr = factory.create_marker_ocr(proxy_type="socks5")
            print("✅ SOCKS5 OCR实例创建成功")
        except Exception as e:
            print(f"⚠️ SOCKS5 OCR实例创建失败: {e}")
        
        try:
            http_ocr = factory.create_marker_ocr(proxy_type="http")
            print("✅ HTTP OCR实例创建成功")
        except Exception as e:
            print(f"⚠️ HTTP OCR实例创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR工厂测试失败: {e}")
        return False

def test_save_comparison_results():
    """测试保存比较结果功能"""
    print("\n🔍 测试保存比较结果...")
    
    try:
        from markerpdf.compare_proxy_types import save_comparison_results
        
        # 创建模拟测试结果
        socks5_result = {
            'proxy_type': 'SOCKS5',
            'success': True,
            'error': None,
            'setup_time': 2.5,
            'processing_time': 45.2,
            'total_time': 47.7,
            'text_length': 1500,
            'output_folder': '/test/output/socks5',
            'attempts': 1
        }
        
        http_result = {
            'proxy_type': 'HTTP',
            'success': True,
            'error': None,
            'setup_time': 3.8,
            'processing_time': 42.1,
            'total_time': 45.9,
            'text_length': 1500,
            'output_folder': '/test/output/http',
            'attempts': 1
        }
        
        # 保存比较结果
        report_file = save_comparison_results(socks5_result, http_result)
        print(f"✅ 比较结果保存成功: {report_file}")
        
        # 检查文件是否存在
        if os.path.exists(report_file):
            print("✅ 报告文件创建成功")
            
            # 读取文件内容检查
            with open(report_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if "代理性能比较报告" in content and "新架构" in content:
                    print("✅ 报告内容格式正确")
                    return True
                else:
                    print("⚠️ 报告内容格式可能有问题")
                    return False
        else:
            print("❌ 报告文件未创建")
            return False
            
    except Exception as e:
        print(f"❌ 保存比较结果测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 代理比较工具测试 (新架构)")
    print("=" * 60)
    
    tests = [
        test_proxy_comparison_imports,
        test_config_validation,
        test_ocr_factory,
        test_save_comparison_results
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！代理比较工具已准备就绪")
        print("\n💡 使用方法:")
        print("  python markerpdf/compare_proxy_types.py")
        print("  python markerpdf/compare_proxy_types.py --pdf your_file.pdf")
        return True
    else:
        print("⚠️ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
