"""
通用代理管理器

提供HTTP和SOCKS5代理管理功能，支持代理转换和环境变量管理
"""

import os
import sys
import urllib.request
import subprocess
import threading
import time
from contextlib import contextmanager
from typing import Optional, Dict, Any


class ProxyManager:
    """代理管理器，确保代理设置的一致性和可靠性"""
    
    def __init__(self):
        self.original_env = {}
        self.proxy_process = None  # 用於存儲 pproxy 進程
        self.http_proxy_port = 8888  # 本地 HTTP 代理端口
        self.proxy_vars = [
            'HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy',
            'ALL_PROXY', 'all_proxy', 'NO_PROXY', 'no_proxy',
            # Add possible NordVPN related variables
            'NORDVPN_PROXY', 'nordvpn_proxy', 'NORDVPN',
            # Add other possible proxy variables
            'FTP_PROXY', 'ftp_proxy', 'SOCKS_PROXY', 'socks_proxy'
        ]
    
    def set_global_proxy(self, proxy_url: str, force: bool = True) -> None:
        """
        设置全局代理，包括所有可能的环境变量

        Args:
            proxy_url: 代理URL，例如 'socks5://user:pass@host:port'
            force: 是否强制覆盖现有代理设置
        """
        print(f"设置全局代理: {proxy_url}")

        # 备份原始环境变量
        for var in self.proxy_vars:
            if var in os.environ:
                self.original_env[var] = os.environ[var]

        # 设置所有可能的代理环境变量（大写和小写）
        proxy_vars_to_set = {
            'HTTP_PROXY': proxy_url,
            'HTTPS_PROXY': proxy_url,
            'http_proxy': proxy_url,
            'https_proxy': proxy_url,
            'ALL_PROXY': proxy_url,
            'all_proxy': proxy_url,
        }

        for var, value in proxy_vars_to_set.items():
            os.environ[var] = value
            print(f"  设置 {var} = {value}")

        print("✅ 全局代理设置完成")

    def set_global_proxy_with_conversion(self, proxy_url: str, force: bool = True) -> None:
        """
        设置全局代理，如果是 SOCKS5 则自动转换为 HTTP

        Args:
            proxy_url: 代理URL，例如 'socks5://user:pass@host:port'
            force: 是否强制覆盖现有代理设置
        """
        print(f"设置全局代理（自动转换）: {proxy_url}")

        # 备份原始环境变量
        for var in self.proxy_vars:
            if var in os.environ:
                self.original_env[var] = os.environ[var]

        # 检查是否为 SOCKS5 代理
        if proxy_url.startswith('socks5://'):
            print("检测到SOCKS5代理，转换为HTTP...")
            http_proxy_url = self._start_socks5_to_http_converter(proxy_url)
            if http_proxy_url:
                proxy_url = http_proxy_url
            else:
                print("⚠️ 启动SOCKS5到HTTP转换器失败，直接使用SOCKS5")

        # 设置代理环境变量
        self.set_global_proxy(proxy_url, force)

    def _start_socks5_to_http_converter(self, socks5_url: str) -> Optional[str]:
        """
        启动SOCKS5到HTTP代理转换器
        
        Args:
            socks5_url: SOCKS5代理URL
            
        Returns:
            HTTP代理URL，如果启动失败则返回None
        """
        try:
            # 停止现有的代理转换器
            self._stop_proxy_converter()
            
            # 解析SOCKS5 URL并转换为pproxy格式
            # socks5://user:pass@host:port -> socks5://user:pass@host:port
            pproxy_url = socks5_url
            
            # 构建pproxy命令
            cmd = [
                sys.executable, "-m", "pproxy",
                "-l", f"http://0.0.0.0:{self.http_proxy_port}",
                "-r", pproxy_url,
                "-v"  # 详细输出用于调试
            ]

            print(f"启动pproxy转换器，端口 {self.http_proxy_port}...")
            self.proxy_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待一段时间让代理启动
            time.sleep(2)
            
            # 检查进程是否还在运行
            if self.proxy_process.poll() is None:
                http_proxy_url = f"http://127.0.0.1:{self.http_proxy_port}"
                print(f"✅ HTTP代理转换器启动成功: {http_proxy_url}")
                return http_proxy_url
            else:
                print("❌ pproxy进程启动失败")
                return None
                
        except Exception as e:
            print(f"❌ 启动代理转换器时出错: {e}")
            return None

    def _stop_proxy_converter(self):
        """停止代理转换器进程"""
        if self.proxy_process:
            try:
                self.proxy_process.terminate()
                self.proxy_process.wait(timeout=5)
                print("🔧 代理转换器已停止")
            except subprocess.TimeoutExpired:
                self.proxy_process.kill()
                print("🔧 强制终止代理转换器")
            except Exception as e:
                print(f"⚠️ 停止代理转换器时出错: {e}")
            finally:
                self.proxy_process = None

    def clear_proxy(self) -> None:
        """清除所有代理设置"""
        print("清除代理设置...")
        
        # 停止代理转换器
        self._stop_proxy_converter()
        
        # 清除代理环境变量
        for var in self.proxy_vars:
            if var in os.environ:
                del os.environ[var]
                print(f"  清除 {var}")
        
        print("✅ 代理设置已清除")

    def restore_original_proxy(self) -> None:
        """恢复原始代理设置"""
        print("恢复原始代理设置...")
        
        # 停止代理转换器
        self._stop_proxy_converter()
        
        # 首先清除当前设置
        for var in self.proxy_vars:
            if var in os.environ:
                del os.environ[var]
        
        # 恢复原始设置
        for var, value in self.original_env.items():
            os.environ[var] = value
            print(f"  恢复 {var} = {value}")
        
        self.original_env.clear()
        print("✅ 原始代理设置已恢复")

    def check_proxy_connectivity(self, test_url: str = "https://www.google.com", timeout: int = 10) -> bool:
        """
        检查代理连接性
        
        Args:
            test_url: 测试URL
            timeout: 超时时间（秒）
            
        Returns:
            代理是否可用
        """
        try:
            print(f"测试代理连接性: {test_url}")
            
            # 创建请求
            request = urllib.request.Request(test_url)
            request.add_header('User-Agent', 'Mozilla/5.0 (compatible; ProxyTest/1.0)')
            
            # 发送请求
            with urllib.request.urlopen(request, timeout=timeout) as response:
                if response.status == 200:
                    print("✅ 代理连接测试成功")
                    return True
                else:
                    print(f"⚠️ 代理连接测试失败，状态码: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"❌ 代理连接测试失败: {e}")
            return False

    @contextmanager
    def proxy_context(self, proxy_url: Optional[str] = None):
        """
        代理上下文管理器，确保进入和退出时正确设置代理
        
        Args:
            proxy_url: 代理URL，如果为None则清除代理
        """
        # 备份当前环境
        original_env = dict(os.environ)
        
        try:
            if proxy_url:
                self.set_global_proxy(proxy_url)
                print(f"进入代理上下文: {proxy_url}")
            else:
                self.clear_proxy()
                print("进入无代理上下文")
            
            yield
            
        finally:
            # 恢复原始环境
            os.environ.clear()
            os.environ.update(original_env)
            print("代理上下文已退出，环境已恢复")

    def __del__(self):
        """确保在对象销毁时停止代理转换器"""
        self._stop_proxy_converter()


def setup_early_proxy(proxy_url: str) -> None:
    """
    早期代理设置，在其他库导入之前设置代理
    
    Args:
        proxy_url: 代理URL
    """
    print(f"早期代理设置: {proxy_url}")
    
    # 设置基本的代理环境变量
    proxy_vars = {
        'HTTP_PROXY': proxy_url,
        'HTTPS_PROXY': proxy_url,
        'http_proxy': proxy_url,
        'https_proxy': proxy_url,
        'ALL_PROXY': proxy_url,
        'all_proxy': proxy_url,
    }
    
    for var, value in proxy_vars.items():
        os.environ[var] = value
    
    print("✅ 早期代理设置完成")


def ensure_proxy_persistence(proxy_url: str) -> None:
    """
    确保代理设置持久化
    
    Args:
        proxy_url: 代理URL
    """
    print(f"确保代理持久化: {proxy_url}")
    
    # 重新设置所有代理变量
    proxy_vars = {
        'HTTP_PROXY': proxy_url,
        'HTTPS_PROXY': proxy_url,
        'http_proxy': proxy_url,
        'https_proxy': proxy_url,
        'ALL_PROXY': proxy_url,
        'all_proxy': proxy_url,
    }
    
    for var, value in proxy_vars.items():
        os.environ[var] = value
    
    print("✅ 代理持久化完成")
