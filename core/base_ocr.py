"""
OCR基础类和接口定义

定义统一的OCR接口，支持不同的OCR实现
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path


@dataclass
class OCRResult:
    """OCR处理结果"""
    model_name: str
    success: bool
    error: Optional[str]
    processing_time: float
    text_length: int
    text_content: str
    output_folder: Optional[str]
    config: Dict[str, Any]
    attempts: int
    image_count: int = 0
    images: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.images is None:
            self.images = {}


class BaseOCR(ABC):
    """OCR基础抽象类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化OCR实例
        
        Args:
            config: OCR配置字典
        """
        self.config = config
        self.model_name = "Unknown OCR"
    
    @abstractmethod
    def process_document(self, pdf_path: str, **kwargs) -> OCRResult:
        """
        处理文档的抽象方法
        
        Args:
            pdf_path: PDF文件路径
            **kwargs: 其他参数
            
        Returns:
            OCR处理结果
        """
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """
        验证配置是否有效
        
        Returns:
            配置是否有效
        """
        pass
    
    def save_output(self, result: OCRResult, output_dir: str) -> str:
        """
        保存OCR输出结果
        
        Args:
            result: OCR结果
            output_dir: 输出目录
            
        Returns:
            输出文件夹路径
        """
        from datetime import datetime
        
        # 创建输出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_name = f"output_{result.model_name.replace(' ', '_')}_{timestamp}"
        output_folder = Path(output_dir) / folder_name
        output_folder.mkdir(parents=True, exist_ok=True)
        
        # 保存文本内容
        text_file = output_folder / "extracted_text.md"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(f"# OCR结果 - {result.model_name}\n\n")
            f.write(f"**处理时间**: {result.processing_time:.2f}秒\n")
            f.write(f"**文本长度**: {result.text_length}字符\n")
            f.write(f"**尝试次数**: {result.attempts}\n\n")
            f.write("## 提取的文本内容\n\n")
            f.write(result.text_content)
        
        # 保存配置信息
        config_file = output_folder / "config.txt"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(f"OCR模型配置 - {result.model_name}\n")
            f.write("=" * 50 + "\n\n")
            for key, value in result.config.items():
                # 确保value是字符串格式
                if isinstance(value, (list, tuple)):
                    value_str = str(value)
                else:
                    value_str = str(value)
                f.write(f"{key}: {value_str}\n")
        
        print(f"✅ 输出已保存到: {output_folder}")
        return str(output_folder)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            "model_name": self.model_name,
            "config": self.config
        }
