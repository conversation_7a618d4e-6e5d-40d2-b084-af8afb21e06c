"""
Azure Document Intelligence配置管理

管理Azure OCR服务的配置
"""

import os
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from dotenv import load_dotenv


@dataclass
class AzureConfig:
    """Azure Document Intelligence配置"""
    azure_endpoint: str
    azure_key: str
    model_id: str
    locale: str
    features: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "azure_endpoint": self.azure_endpoint,
            "azure_key": self.azure_key,
            "model_id": self.model_id,
            "locale": self.locale,
            "features": self.features
        }


class AzureConfigManager:
    """Azure配置管理器"""
    
    def __init__(self, env_file: Optional[str] = None):
        """
        初始化Azure配置管理器
        
        Args:
            env_file: 环境变量文件路径，默认为None（使用默认.env文件）
        """
        if env_file:
            load_dotenv(env_file)
        else:
            load_dotenv()
    
    def create_azure_config(self) -> Optional[AzureConfig]:
        """创建Azure配置"""
        endpoint = os.getenv('AZURE_DOCINTEL_ENDPOINT')
        key = os.getenv('AZURE_DOCINTEL_KEY')
        
        if not endpoint or not key:
            print("⚠️ Azure配置不完整：缺少端点或密钥")
            return None
        
        return AzureConfig(
            azure_endpoint=endpoint,
            azure_key=key,
            model_id=os.getenv('AZURE_MODEL_ID', 'prebuilt-layout'),
            locale=os.getenv('AZURE_LOCALE', 'en-US'),
            features=["MATH_SOLVER"]  # 默认启用数学公式识别
        )
    
    def validate_config(self) -> bool:
        """验证Azure配置是否完整"""
        config = self.create_azure_config()
        return config is not None
