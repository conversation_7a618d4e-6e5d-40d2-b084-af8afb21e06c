"""
Marker OCR HTTP代理实现

使用HTTP代理（从SOCKS5转换）的Marker OCR服务
"""

import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser
from marker.output import text_from_rendered

from core.base_ocr import BaseOCR, OCRResult
from proxy import ProxyManager


class MarkerHTTPOCR(BaseOCR):
    """Marker OCR HTTP代理实现"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_name = "Gemini 2.5 Flash (HTTP Proxy)"
        self.proxy_manager = ProxyManager()
    
    def validate_config(self) -> bool:
        """验证Gemini配置"""
        required_keys = ['gemini_api_key', 'llm_service']
        return all(key in self.config and self.config[key] for key in required_keys)
    
    def process_document(self, pdf_path: str, max_retries: int = 3, **kwargs) -> OCRResult:
        """
        使用HTTP代理处理PDF文档
        
        Args:
            pdf_path: PDF文件路径
            max_retries: 最大重试次数
            **kwargs: 其他参数
            
        Returns:
            OCR处理结果
        """
        print(f"\n{'='*50}")
        print(f"开始 {self.model_name} OCR处理")
        print(f"文件: {pdf_path}")
        print(f"{'='*50}")
        
        if not self.validate_config():
            return OCRResult(
                model_name=self.model_name,
                success=False,
                error="配置验证失败",
                processing_time=0,
                text_length=0,
                text_content="",
                output_folder=None,
                config=self.config,
                attempts=0
            )
        
        # 确保代理设置
        proxy_url = os.getenv('NORDVPN_PROXY')
        if proxy_url:
            self.proxy_manager.set_global_proxy_with_conversion(proxy_url, force=True)
            print("✅ HTTP代理设置完成")
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    print(f"\n第 {attempt + 1} 次尝试...")
                    # 重新设置代理
                    if proxy_url:
                        self.proxy_manager.set_global_proxy_with_conversion(proxy_url, force=True)
                        print("🔄 代理已重置")
                
                start_time = time.time()
                
                # 创建配置解析器和转换器
                config_parser = ConfigParser(self.config)
                converter = PdfConverter(
                    config=config_parser.generate_config_dict(),
                    artifact_dict=create_model_dict(),
                    processor_list=config_parser.get_processors(),
                    renderer=config_parser.get_renderer(),
                    llm_service=config_parser.get_llm_service()
                )
                
                print("🔄 开始PDF转换...")
                
                # 执行转换
                rendered = converter(pdf_path)
                text_content = text_from_rendered(rendered)
                
                processing_time = time.time() - start_time
                
                print(f"✅ 转换完成！")
                print(f"⏱️ 处理时间: {processing_time:.2f}秒")
                print(f"📄 提取文本长度: {len(text_content)}字符")
                
                # 保存输出
                output_folder = self.save_output(OCRResult(
                    model_name=self.model_name,
                    success=True,
                    error=None,
                    processing_time=processing_time,
                    text_length=len(text_content),
                    text_content=text_content,
                    output_folder=None,
                    config=self.config,
                    attempts=attempt + 1,
                    image_count=len(rendered.images) if hasattr(rendered, 'images') else 0,
                    images=rendered.images if hasattr(rendered, 'images') else {}
                ), ".")
                
                return OCRResult(
                    model_name=self.model_name,
                    success=True,
                    error=None,
                    processing_time=processing_time,
                    text_length=len(text_content),
                    text_content=text_content,
                    output_folder=output_folder,
                    config=self.config,
                    attempts=attempt + 1,
                    image_count=len(rendered.images) if hasattr(rendered, 'images') else 0,
                    images=rendered.images if hasattr(rendered, 'images') else {}
                )
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ 第 {attempt + 1} 次尝试失败: {error_msg}")
                
                if attempt == max_retries - 1:
                    # 最后一次尝试失败
                    return OCRResult(
                        model_name=self.model_name,
                        success=False,
                        error=error_msg,
                        processing_time=0,
                        text_length=0,
                        text_content="",
                        output_folder=None,
                        config=self.config,
                        attempts=attempt + 1
                    )
                
                # 等待后重试
                time.sleep(2)
        
        # 不应该到达这里
        return OCRResult(
            model_name=self.model_name,
            success=False,
            error="未知错误",
            processing_time=0,
            text_length=0,
            text_content="",
            output_folder=None,
            config=self.config,
            attempts=max_retries
        )
